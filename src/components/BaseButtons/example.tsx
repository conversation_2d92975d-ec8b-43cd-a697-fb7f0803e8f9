import React, { useState } from 'react';
import { Card, Row, Col, Space, Switch, InputNumber } from 'antd';
import { BaseButtons } from './index';
import type { BaseButtonConfig } from './types';

const Example: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [maxCount, setMaxCount] = useState(3);
    const [rowData, setRowData] = useState({
        id: 1,
        name: '测试数据',
        canEdit: true,
        canDelete: true,
        status: 'active',
    });

    const handleEdit = async (row: any) => {
        console.log('编辑操作', row);
        // 模拟异步操作
        await new Promise((resolve) => setTimeout(resolve, 1000));
    };

    const handleDelete = async (row: any) => {
        console.log('删除操作', row);
        await new Promise((resolve) => setTimeout(resolve, 1500));
    };

    const handleView = async (row: any) => {
        console.log('查看详情', row);
    };

    const handleExport = async (row: any) => {
        console.log('导出数据', row);
        await new Promise((resolve) => setTimeout(resolve, 800));
    };

    const handleApprove = async (row: any) => {
        console.log('审批操作', row);
        await new Promise((resolve) => setTimeout(resolve, 1200));
    };

    // 基础按钮配置
    const basicButtons: BaseButtonConfig[] = [
        {
            label: '编辑',
            props: { type: 'primary', size: 'small' },
            event: handleEdit,
            show: (row) => row.canEdit,
        },
        {
            label: '删除',
            props: { danger: true, size: 'small' },
            event: handleDelete,
            show: (row) => row.canDelete,
        },
        {
            label: '查看',
            props: { size: 'small' },
            event: handleView,
        },
    ];
    const basicButtons2: BaseButtonConfig[] = [
        {
            label: '编辑',
            props: { type: 'link', size: 'small' },
            event: handleEdit,
            show: (row) => row.canEdit,
        },
        {
            label: '删除',
            props: { type: 'link', color: 'danger', size: 'small' },
            event: handleDelete,
            show: (row) => row.canDelete,
        },
        {
            label: '查看',
            props: { type: 'link', color: 'default', size: 'small' },
            event: handleView,
        },
    ];

    const basicButtons3: BaseButtonConfig[] = [
        {
            label: '更多操作',
            props: { size: 'small' },
            children: [
                {
                    label: '导出',
                    props: { type: 'text' },
                    event: handleExport,
                    show: (row) => row.canEdit,
                },
                {
                    label: '审批',
                    props: { type: 'text' },
                    event: handleApprove,
                },
                {
                    label: '归档',
                    props: { type: 'text' },
                    event: (row) => console.log('归档', row),
                },
            ],
        },
    ];

    // 带气泡按钮的配置
    const advancedButtons: BaseButtonConfig[] = [
        ...basicButtons,
        {
            label: '复制',
            props: { size: 'small' },
            event: (row) => console.log('复制', row),
            show: (row) => row.canEdit,
        },
        {
            label: '移动',
            props: { size: 'small' },
            event: (row) => console.log('移动', row),
            show: (row) => row.canEdit,
        },
    ];

    return (
        <div style={{ padding: 24 }}>
            <Row gutter={[16, 8]}>
                <Col span={12}>
                    <Card title="基础用法" size="small">
                        <BaseButtons btns={basicButtons} row={rowData} loading={loading} />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="函数式配置" size="small">
                        <BaseButtons
                            btns={(row) => [
                                {
                                    label: `编辑 ${row.name}`,
                                    props: { type: 'primary', size: 'small' },
                                    event: handleEdit,
                                },
                                {
                                    label: '动态按钮',
                                    props: { size: 'small' },
                                    event: (row) => console.log('动态按钮点击', row),
                                    show: row.canEdit,
                                },
                            ]}
                            loading={loading}
                            row={rowData}
                        />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="文本类型按钮（常用作表格操作列）" size="small">
                        <BaseButtons btns={basicButtons2} row={rowData} loading={loading} />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="聚合按钮(PopoverButton)" size="small">
                        <BaseButtons btns={basicButtons3} row={rowData} loading={loading} />
                    </Card>
                </Col>

                <Col span={24}>
                    <Card title="较多的按钮" size="small">
                        <BaseButtons
                            btns={advancedButtons}
                            row={rowData}
                            maxCount={maxCount}
                            loading={loading}
                        />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="flex布局" size="small">
                        <BaseButtons
                            btns={basicButtons}
                            row={rowData}
                            loading={loading}
                            rowProps={{
                                justify: 'space-between',
                                align: 'middle',
                            }}
                        />
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="栅格布局" size="small">
                        <BaseButtons
                            btns={basicButtons}
                            row={rowData}
                            loading={loading}
                            colProps={{
                                span: 6,
                            }}
                        />
                    </Card>
                </Col>

                <Col span={24}>
                    <Card title="控制面板" size="small">
                        <Space>
                            <span>全局Loading:</span>
                            <Switch checked={loading} onChange={setLoading} />

                            <span>最大显示数量:</span>
                            <InputNumber
                                min={1}
                                max={10}
                                value={maxCount}
                                onChange={(value) => setMaxCount(value || 3)}
                            />

                            <span>可编辑:</span>
                            <Switch
                                checked={rowData.canEdit}
                                onChange={(checked) =>
                                    setRowData((prev) => ({ ...prev, canEdit: checked }))
                                }
                            />

                            <span>可删除:</span>
                            <Switch
                                checked={rowData.canDelete}
                                onChange={(checked) =>
                                    setRowData((prev) => ({ ...prev, canDelete: checked }))
                                }
                            />
                        </Space>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default Example;
