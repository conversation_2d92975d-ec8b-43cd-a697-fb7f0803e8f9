import React, { useState } from 'react';
import { Button } from './deps';
import { noBorderButtonProp } from './deps';
import { CustomButtonProps } from './types';
import './BaseButtons.less';

const CustomButton: React.FC<CustomButtonProps> = ({
    btn,
    row = {},
    pLoading = false,
    className = '',
    children,
    ...restProps
}) => {
    const [loading, setLoading] = useState(false);

    const handleButtonClick = async (e: React.MouseEvent) => {
        if (!btn.event) return;

        try {
            setLoading(true);
            await btn.event(row, e);
        } catch (error) {
            console.error('Error executing button event:', error);
        } finally {
            setLoading(false);
        }
    };

    const getClassName = () => {
        const cls = ['customButton'];

        // 检查是否为文本按钮
        const isTextButton =
            btn?.props?.[Object.keys(noBorderButtonProp)[0] as keyof typeof btn.props] ===
            Object.values(noBorderButtonProp)[0];

        if (isTextButton) {
            cls.push('customButtonText');
        }

        if (className) {
            cls.push(className);
        }

        return cls.join(' ');
    };

    return (
        <Button
            {...restProps}
            {...(btn?.props || {})}
            className={getClassName()}
            onClick={handleButtonClick}
            loading={loading || pLoading}
        >
            {children || btn?.label}
        </Button>
    );
};

export default CustomButton;
