# BaseButtons - Umi3.5 + Antd 版本

基于 umi3.5 和 antd 的按钮组组件，从原 Vue 版本迁移而来。

## 功能特性

1. **配置化渲染** - 通过配置项进行按钮渲染，支持函数式动态配置
2. **布局支持** - 支持 antd Row/Col 组件的所有布局配置，包括flex和栅格布局
3. **最大显示限制** - 支持最大显示按钮数限制，超过则通过气泡按钮展示
4. **Loading 状态** - 支持按钮组整体和单个按钮的 loading 状态
5. **条件显示** - 支持按钮的条件显示逻辑，支持函数式判断
6. **嵌套按钮** - 支持气泡按钮组，可将多个按钮聚合在一个气泡中
7. **异步事件处理** - 自动处理异步事件的loading状态
8. **样式定制** - 支持自定义样式和CSS类名

## Props

### BaseButtonsProps

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| btns | `BaseButtonConfig[]` \| `(row: any) => BaseButtonConfig[]` | - | 按钮组数据，用以渲染按钮，支持数组或函数形式 |
| row | `any` | `{}` | 按钮所在模块的数据，如行数据，会传递给按钮事件和显示判断函数 |
| rowProps | `RowProps` | `{ type: 'flex', justify: 'start', align: 'bottom' }` | Row组件布局配置，参考 antd Row 组件 |
| colProps | `ColProps` | - | Col组件配置，设置后按钮将使用栅格布局 |
| maxCount | `number` | `5` | 最多显示几个按钮，超出的将通过省略号按钮展示 |
| loading | `boolean` | `false` | 按钮组整体 loading 状态，会禁用所有按钮 |
| popoverProps | `PopoverProps` | `{}` | 气泡框配置，应用于所有气泡按钮 |
| ellipsisSpaceProps | `SpaceProps` | `{ direction: 'vertical' }` | 省略号按钮内部Space组件配置 |

### BaseButtonConfig

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| label | `string` | - | 按钮显示文本 |
| props | `ButtonProps` | - | 按钮属性，参考 antd Button 组件文档，支持所有Button属性 |
| style | `React.CSSProperties` | - | 按钮自定义样式 |
| event | `(row: any, e?: React.MouseEvent) => Promise<void> \| void` | - | 按钮点击事件，支持异步函数，会自动处理loading状态 |
| show | `boolean \| (row: any) => boolean` | `true` | 按钮是否显示，支持函数式动态判断 |
| children | `BaseButtonConfig[]` | - | 子按钮配置，设置后将渲染为气泡按钮组 |

### 其他相关类型

#### PopoverButtonProps
气泡按钮组件的属性配置

#### CustomButtonProps
自定义按钮组件的属性配置

## 使用示例
注：可使用 `import BaseButtonsExample from '@/components/BaseButtons/example';` 将example文件引入页面中查看效果
### 基础用法

```tsx
import React from 'react';
import { BaseButtons } from '@/components/BaseButtons';

const BasicExample = () => {
    const handleEdit = async (row: any) => {
        console.log('编辑操作', row);
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 1000));
    };

    const handleDelete = async (row: any) => {
        console.log('删除操作', row);
    };

    const buttons = [
        {
            label: '编辑',
            props: { type: 'primary', size: 'small' },
            event: handleEdit,
            show: (row) => row.canEdit,
        },
        {
            label: '删除',
            props: { danger: true, size: 'small' },
            event: handleDelete,
            show: (row) => row.canDelete,
        },
        {
            label: '查看',
            props: { size: 'small' },
            event: (row) => console.log('查看', row),
        },
    ];

    const rowData = {
        id: 1,
        name: '测试数据',
        canEdit: true,
        canDelete: true
    };

    return <BaseButtons btns={buttons} row={rowData} />;
};
```

### 函数式配置

```tsx
const FunctionExample = () => {
    return (
        <BaseButtons
            btns={(row) => [
                {
                    label: `编辑 ${row.name}`,
                    props: { type: 'primary', size: 'small' },
                    event: handleEdit,
                },
                {
                    label: '动态按钮',
                    props: { size: 'small' },
                    event: (row) => console.log('动态按钮点击', row),
                    show: row.canEdit,
                },
            ]}
            row={rowData}
        />
    );
};
```

### 文本类型按钮（常用作表格操作列）

```tsx
const TextButtonExample = () => {
    const textButtons = [
        {
            label: '编辑',
            props: { type: 'link', size: 'small' },
            event: handleEdit,
            show: (row) => row.canEdit,
        },
        {
            label: '删除',
            props: { type: 'link', color: 'danger', size: 'small' },
            event: handleDelete,
            show: (row) => row.canDelete,
        },
        {
            label: '查看',
            props: { type: 'link', color: 'default', size: 'small' },
            event: handleView,
        },
    ];

    return <BaseButtons btns={textButtons} row={rowData} />;
};
```

### 气泡按钮组（PopoverButton）

```tsx
const PopoverExample = () => {
    const popoverButtons = [
        {
            label: '更多操作',
            props: { size: 'small' },
            children: [
                {
                    label: '导出',
                    props: { type: 'text' },
                    event: handleExport,
                    show: (row) => row.canEdit,
                },
                {
                    label: '审批',
                    props: { type: 'text' },
                    event: handleApprove,
                },
                {
                    label: '归档',
                    props: { type: 'text' },
                    event: (row) => console.log('归档', row),
                },
            ],
        },
    ];

    return <BaseButtons btns={popoverButtons} row={rowData} />;
};
```

### 布局配置示例

```tsx
// Flex布局
const FlexLayoutExample = () => (
    <BaseButtons
        btns={buttons}
        row={rowData}
        rowProps={{
            justify: 'space-between',
            align: 'middle',
        }}
    />
);

// 栅格布局
const GridLayoutExample = () => (
    <BaseButtons
        btns={buttons}
        row={rowData}
        colProps={{
            span: 6,
        }}
    />
);
```

### 最大显示数量限制

```tsx
const MaxCountExample = () => {
    const manyButtons = [
        { label: '编辑', props: { type: 'primary', size: 'small' }, event: handleEdit },
        { label: '删除', props: { danger: true, size: 'small' }, event: handleDelete },
        { label: '查看', props: { size: 'small' }, event: handleView },
        { label: '复制', props: { size: 'small' }, event: handleCopy },
        { label: '移动', props: { size: 'small' }, event: handleMove },
    ];

    return (
        <BaseButtons
            btns={manyButtons}
            row={rowData}
            maxCount={3} // 最多显示3个按钮，其余的会放在省略号按钮中
        />
    );
};
```

## 组件架构

BaseButtons 组件由以下几个子组件组成，组件层级由高到低，可单独使用：

- **BaseButtons**: 
  - 主组件，负责按钮组的整体布局和最大显示数量相关逻辑
- **PopoverButton**: 
  - 气泡按钮组件，处理有子按钮的情况
- **CustomButton**: 
  - 自定义按钮组件，处理单个按钮的渲染和事件
  - 主要用于劫持onClick事件从而实现自动化loading

## 样式定制

组件提供了以下CSS类名用于样式定制：

- `.BaseButtons`: 主容器样式
- `.btnMl12`: 按钮间距样式
- `.ellipsisButton`: 省略号按钮样式
- `.ellipsisButtonDot`: 省略号图标样式
- `.customButton`: 自定义按钮样式
- `.customButtonText`: 文本按钮样式

## 迁移说明

从 Vue 版本迁移到 React 版本的主要变化：

1. **框架变更**: Vue → React + TypeScript
2. **UI 库变更**: Element Plus → Antd
3. **状态管理**: Vue 响应式 → React Hooks
4. **样式**: Vue scoped CSS → CSS Modules
5. **插槽**: Vue slots → React children props
6. **事件处理**: Vue 事件修饰符 → React 事件对象
7. **条件渲染**: Vue v-if → React 条件表达式

## 最佳实践

1. **异步事件处理**: 建议使用异步函数处理按钮事件，组件会自动管理loading状态
2. **条件显示**: 使用函数式show配置可以实现动态显示逻辑
3. **按钮分组**: 对于相关的操作按钮，可以使用children属性创建气泡按钮组
4. **样式一致性**: 建议在同一个按钮组中使用相同的size属性保持视觉一致性
5. **权限控制**: 通过show属性结合用户权限数据实现按钮的权限控制

## 注意事项

1. **依赖要求**: 确保项目已安装 `antd` 依赖
2. **样式支持**: 确保项目支持 CSS Modules (umi3.5 默认支持)
3. **异步处理**: 按钮事件函数建议使用异步函数，以便自动控制 loading 状态
4. **性能优化**: 当btns为函数时，避免在函数内创建复杂对象，建议使用useMemo优化
5. **类型安全**: 建议为row数据定义明确的TypeScript类型
6. **事件处理**: 按钮事件中的异常会被自动捕获并打印到控制台

## 常见问题

### Q: 如何自定义省略号按钮的样式？
A: 可以通过CSS覆盖`.ellipsisButton`和`.ellipsisButtonDot`类名的样式。

### Q: 如何实现按钮的权限控制？
A: 使用show属性结合权限数据：
```tsx
{
    label: '删除',
    show: (row) => userPermissions.includes('delete') && row.canDelete,
    // ...
}
```

### Q: 气泡按钮中的子按钮如何响应row数据变化？
A: 目前存在已知问题，气泡按钮中的子按钮不能完全响应row值的动态变化，建议避免在children中使用依赖row的复杂逻辑。

### Q: 如何处理按钮事件中的错误？
A: 组件会自动捕获事件函数中的错误并打印到控制台，你也可以在事件函数中添加自己的错误处理逻辑。
