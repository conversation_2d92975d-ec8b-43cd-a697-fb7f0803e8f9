import type { ProFormInstance } from '@ant-design/pro-form';
import { DownloadOutlined, SyncOutlined } from '@ant-design/icons';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import type { ActionType, ProColumnType } from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { Button, Card, message, Space, Tag, Typography, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import { Link } from 'umi';
import moment from 'moment';
import CacheAreaView from '@/components/CacheAreaView';
import type { Moment } from 'moment';
import XdtProTable from '@/components/XdtProTable';
import AppealActions from './components/AppealActions';
import {
    queryAppealListApi,
    exportAppealListApi,
    getAppealStatisticsApi,
} from '@/services/Merchant/AppealManagementApi';
import {
    AppealStatusEnum,
    AppealStatus,
    AppealType,
    PayAppeal,
    PayStatus,
    PayType,
} from '@/constants/AppealManagement';
import { mockAppealListResponse } from './mockData';
import ButtonExample from '@/components/BaseButtons/example';

const AppealListPage = () => {
    const actionRef = useRef<ActionType>();
    const formRef = useRef<ProFormInstance>();
    const cacheRef = useRef();
    // const [searchParams, setSearchParams] = useState<any>({});

    // 获取申诉列表数据
    const { runAsync: getList, refresh: refreshList } = useRequest(
        async (params) => {
            try {
                return await queryAppealListApi(params);
            } catch (error) {
                console.warn('API调用失败，使用mock数据:', error);
                return await mockAppealListResponse(params);
            }
        },
        {
            manual: true,
        },
    );

    // 导出功能
    const { run: exportData, loading: exportLoading } = useRequest(exportAppealListApi, {
        manual: true,
        onSuccess: () => {
            message.success('导出成功');
        },
        onError: () => {
            message.error('导出失败');
        },
    });

    const handleExport = async () => {
        const params = formRef?.current?.getFieldsFormatValue?.(true);
        const res = await getList(params);
        console.log('params', res);
        // exportData(params);
    };
    type RangeValue = [Moment | null, Moment | null] | null;
    const [dates, setDates] = useState<RangeValue>(null);
    const [appealTimeValue, setAppealTimeValue] = useState<RangeValue>(null);

    const disabledDate = (current: Moment) => {
        if (!dates) {
            return false;
        }
        const tooLate = dates[0] && current.diff(dates[0], 'days') > 30;
        const tooEarly = dates[1] && dates[1].diff(current, 'days') > 30;
        return !!tooEarly || !!tooLate;
    };

    const onOpenChange = (open: boolean) => {
        if (open) {
            setDates([null, null]);
        } else {
            setDates(null);
        }
    };

    const columns: ProColumnType<API.AppealListItem>[] = [
        {
            title: '申诉编号',
            dataIndex: 'appealNo',
            width: 160,
            fixed: 'left',
            hideInSearch: true,
            render: (text, record) => (
                <Link to={`/merchant/appealManagement/detail?appealNo=${record.appealNo}`}>
                    {text}
                </Link>
            ),
        },
        {
            title: (_, type) => {
                return type !== 'form' ? '申诉用户' : '手机号';
            },
            dataIndex: 'userPhone',
            width: 120,
            fieldProps: {
                placeholder: '全部',
            },
            formItemProps: {
                name: 'userPhone',
            },
            render: (text) => text || '-',
        },
        {
            title: '运营商名称',
            dataIndex: 'operatorName',
            width: 140,
            render: (text) => text || '-',
        },
        {
            title: '申诉类别',
            dataIndex: 'appealType',
            width: 120,
            valueEnum: () => AppealType,
            renderText: (text) => {
                return text ?? '-';
            },
            // renderText: (text) => AppealType[text as keyof typeof AppealType] || text || '-',
        },
        {
            title: '场站名称',
            dataIndex: 'stationName',
            width: 150,
            ellipsis: true,
            render: (text) => <Tooltip title={text}>{text || '-'}</Tooltip>,
        },
        {
            title: '申诉时间',
            dataIndex: 'appealTime',
            width: 160,
            valueType: 'dateRange',
            fieldProps: {
                placeholder: ['开始日期', '结束日期'],
                value: dates || appealTimeValue,
                onChange: (val: RangeValue) => setAppealTimeValue(val),
                onCalendarChange: (val: RangeValue) => setDates(val),
                onOpenChange,
                disabledDate,
                allowEmpty: [true, false],
            },
            renderText: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'),
        },
        {
            title: '申诉金额(元)',
            dataIndex: 'appealAmount',
            hideInSearch: true,
            width: 110,
            render: (text) => (text ? `¥${text}` : '-'),
        },
        {
            title: '赔付形式',
            dataIndex: 'compensationForm',
            hideInSearch: true,
            width: 100,
            valueEnum: () => PayType,
            render: (text) => text || '-',
        },
        {
            title: '车牌号',
            dataIndex: 'licensePlateNumber',
            width: 120,
            render: (text) => text || '-',
        },
        {
            title: '订单编号',
            dataIndex: 'orderNo',
            width: 160,
            render: (text) => text || '-',
        },
        {
            title: '申诉状态',
            dataIndex: 'appealStatus',
            width: 100,
            valueEnum: () => AppealStatus,
            renderText: (text) => text || '-',
            // <Tag color={getStatusColor(text)}>
            //     {AppealStatus[text as keyof typeof AppealStatus] || text || '-'}
            // </Tag>
        },
        {
            title: '赔付状态',
            dataIndex: 'compensationStatus',
            width: 100,
            valueEnum: () => PayStatus,
            renderText: (text) => text || '-',
        },
        {
            title: '赔付同申诉',
            dataIndex: 'isSame',
            hideInSearch: true,
            width: 100,
            valueEnum: () => PayAppeal,
            renderText: (text) => text || '-',
        },
        {
            title: '操作',
            key: 'action',
            hideInSearch: true,
            width: 150,
            fixed: 'right',
            render: (_, record) => (
                <AppealActions
                    record={record}
                    onSuccess={() => {
                        actionRef.current?.reload();
                        refreshList();
                    }}
                />
            ),
        },
    ];

    return (
        <PageHeaderWrapper extra={<CacheAreaView bizType={'orderQuery'} initRef={cacheRef} />}>
            {/* 数据表格 */}
            <XdtProTable
                actionRef={actionRef as any}
                columns={columns as any}
                formRef={formRef}
                requestApi={getList}
                rowKey="id"
                scroll={{ x: 1800 }}
                // value={ searchParams}
                // onChange= {(value) => setSearchParams(value)}
                toolButtons={[
                    <Button
                        key="export"
                        icon={<DownloadOutlined />}
                        onClick={handleExport}
                        loading={exportLoading}
                    >
                        导出
                    </Button>,
                    <ButtonExample />,
                ]}
            />
        </PageHeaderWrapper>
    );
};

export default AppealListPage;
